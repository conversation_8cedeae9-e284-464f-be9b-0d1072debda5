// ==================================================
// LPOが必要なケース
// ==================================================
.lpo-support {
  .lpo-check {
    @include lpo-section;

    position: relative;
    background-color: $lpo-color-bg-light-grean;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 33rem;
      pointer-events: none;
      content: '';
      background-color: $lpo-color-bg-light-gray;
      background-image:
        linear-gradient(to right, $lpo-color-bg-grid-line 0.5px, transparent 0.5px),
        linear-gradient(to bottom, $lpo-color-bg-grid-line 0.5px, transparent 0.5px);
      background-size: 3rem 3rem;
    }

    &::after {
      position: absolute;
      bottom: -2rem;
      left: 50%;
      z-index: 2;
      width: 4rem;
      height: 4rem;
      content: '';
      background-color: $lpo-color-bg-light-grean;
      transform: translateX(-50%) rotate(135deg) skew(10deg, 10deg);
    }

    &__container {
      @include lpo-container;
    }

    &__title {
      display: flex;
      flex-direction: column;
      align-items: center;
      line-height: 1;
    }

    &__check-list {
      position: relative;
      display: flex;
      flex-wrap: wrap;
      gap: 0.6rem;
      row-gap: 7rem;
      justify-content: center;
      width: 100%;
      margin-top: 10rem;

      &::before {
        position: absolute;
        top: -8rem;
        left: 50%;
        display: block;
        width: 6.3px;
        height: 3.6rem;
        content: '';
        background-image: url('../../images/lpo/case-dot.svg');
        background-repeat: no-repeat;
        background-size: contain;
        transform: translateX(-50%);
      }

      .check-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: calc(33% - 0.2rem);
        max-height: 15.8rem;
        padding: 5.6rem 0 4.8rem;
        background-color: #fff;
        border: 1px solid $lpo-color-border-green;
        border-bottom: 1px solid $lpo-color-bg-grid-line;
        box-shadow: 0 0 14px 0 rgb(125 200 182 / 40%);

        &::after {
          position: absolute;
          top: -3.4rem;
          left: 50%;
          width: 6.8rem;
          height: 6.8rem;
          content: '';
          background-image: url('../../images/lpo/case-check-icon.svg');
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          transform: translateX(-50%);
        }

        &__text {
          @include lpo-text;
        }
      }
    }
  }
}
