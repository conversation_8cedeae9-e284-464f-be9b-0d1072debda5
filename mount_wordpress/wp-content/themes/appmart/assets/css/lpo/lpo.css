@charset "UTF-8";
/* stylelint-disable scss/dollar-variable-colon-space-after */
@import 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100;300;400;500;700;900&display=swap';
@import 'https://fonts.googleapis.com/css2?family=Yu+Gothic:wght@300;400;500;700&display=swap';
@media (max-width: 768px) {
  .u-pc-only {
    display: none !important;
  }
}

.u-sp-only {
  display: none !important;
}
@media (max-width: 768px) {
  .u-sp-only {
    display: block !important;
  }
}
@media (max-width: 768px) {
  .u-sp-only.inline {
    display: inline-flex !important;
  }
}

.u-underline {
  position: relative;
  display: inline-block;
}
.u-underline::before {
  position: absolute;
  bottom: -4px;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 14px;
  content: "";
  background-color: #fff54b;
  border-radius: 7px;
}
.u-underline--mint::before {
  bottom: -4px;
  height: 18px;
  background-color: #b1e2d5;
  border-radius: unset;
}
.u-underline--none::before {
  display: none;
}

.u-fs__2 {
  font-size: 2rem;
}

.u-text-dot-char {
  position: relative;
  display: inline-block;
}
.u-text-dot-char::before {
  position: absolute;
  top: -12px;
  left: 50%;
  width: 8px;
  height: 8px;
  content: "";
  background-color: #fa6b58;
  border-radius: 50%;
  transform: translateX(-50%);
}

html {
  font-size: 62.5% !important;
}

.lpo-support {
  padding-top: 8rem;
}

.lpo-support * {
  box-sizing: border-box;
  font-family: "Noto Sans JP", helvetica, sans-serif;
}

.lpo-cta-button-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background-color: transparent;
}

.lpo-cta-button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 362px;
  height: 92px;
  background-color: #fa6b58;
  border-radius: 46px;
  box-shadow: 0 4px 19px rgba(82, 135, 121, 0.13);
}
.lpo-cta-button::before {
  position: absolute;
  top: 50%;
  right: 20%;
  width: 9px;
  height: 9px;
  content: "";
  border-top: 3px solid #fff;
  border-right: 3px solid #fff;
  border-radius: 0 6px;
  transform: translateY(-25%) rotate(45deg);
}
.lpo-cta-button__text {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  pointer-events: none;
}
.lpo-cta-button:hover {
  color: #fa6b58;
  background-color: #fff;
  border: 2px solid #fa6b58;
  transition: all 0.3s ease;
}
.lpo-cta-button:hover .lpo-cta-button__text {
  color: #fa6b58;
}
.lpo-cta-button:hover::before {
  border-top: 3px solid #fa6b58;
  border-right: 3px solid #fa6b58;
}

.lpo-support .lpo-fv {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  z-index: 0;
  display: flex;
  flex-direction: column;
  padding-bottom: 6rem;
  overflow: hidden;
  background-color: #b1e2d5;
  background-image: url("../../images/lpo/fv-bg-dot.svg");
  background-repeat: repeat;
  background-position: center;
  background-size: 8px 8px;
}
@media (max-width: 480px) {
  .lpo-support .lpo-fv {
    padding: 80px 0 40px;
  }
}
.lpo-support .lpo-fv::before {
  position: absolute;
  top: -450px;
  left: 50%;
  width: 2523px;
  height: 1246px;
  content: "";
  background-color: #f9f9f9;
  border-radius: 50%;
  transform: translateX(-50%);
}
.lpo-support .lpo-fv__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1632px;
  padding: 0 16px;
  margin: 0 auto;
  position: relative;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding-left: 250px;
}
@media (max-width: 768px) {
  .lpo-support .lpo-fv__container {
    max-width: 100%;
  }
}
.lpo-support .lpo-fv__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-support .lpo-fv__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__container .lpo-section-title.black {
  color: #333;
}
.lpo-support .lpo-fv__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-support .lpo-fv__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-support .lpo-fv__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-support .lpo-fv__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-support .lpo-fv__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-support .lpo-fv__container::before {
  position: absolute;
  top: -50px;
  right: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-image: url("../../images/lpo/fv-bg-pc.png");
  background-repeat: no-repeat;
  background-position: center;
}
.lpo-support .lpo-fv__container .fv-title {
  z-index: 1;
  min-height: 685px;
}
.lpo-support .lpo-fv__container .fv-title__catch {
  display: flex;
  gap: 8px;
  align-items: center;
}
.lpo-support .lpo-fv__container .fv-title__catch--accent {
  z-index: 1;
  display: inline-block;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 454px;
  height: 55px;
  padding-bottom: 8px;
  color: #3ab795;
  background-image: url("../../images/lpo/fv-catch-accent-bg.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
}
.lpo-support .lpo-fv__container .fv-title__catch--accent.white {
  color: #fff;
}
.lpo-support .lpo-fv__container .fv-title__catch--accent.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__container .fv-title__catch--accent.black {
  color: #333;
}
.lpo-support .lpo-fv__container .fv-title__catch--accent.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__container .fv-title__catch--accent.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__container .fv-title__catch--accent.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__container .fv-title__catch--accent.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__container .fv-title__catch--text {
  color: #333;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-sm {
  z-index: 1;
  display: inline-block;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-sm.white {
  color: #fff;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-sm.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-sm.black {
  color: #333;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-sm.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-sm.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-sm.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-sm.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-md {
  z-index: 1;
  display: inline-block;
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-md.white {
  color: #fff;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-md.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-md.black {
  color: #333;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-md.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-md.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-md.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-md.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-lg {
  z-index: 1;
  display: inline-block;
  font-size: 6rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-lg.white {
  color: #fff;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-lg.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-lg.black {
  color: #333;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-lg.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-lg.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-lg.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-lg.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xl {
  z-index: 1;
  display: inline-block;
  font-size: 10rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xl.white {
  color: #fff;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xl.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xl.black {
  color: #333;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xl.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xl.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xl.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xl.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xxl {
  z-index: 1;
  display: inline-block;
  font-size: 13rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xxl.white {
  color: #fff;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xxl.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xxl.black {
  color: #333;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xxl.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xxl.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xxl.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__container .fv-title__catch--text-xxl.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__container .form-container {
  position: absolute;
  top: 0;
  right: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 375px;
  height: 696px;
  font-size: 2.2rem;
  background-color: #d9d9d9;
}
.lpo-support .lpo-fv__message {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding-top: 9rem;
  margin-bottom: 40px;
}
.lpo-support .lpo-fv__message-text {
  display: flex;
  align-items: flex-end;
}
.lpo-support .lpo-fv__message-text--1 {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-fv__message-text--1.white {
  color: #fff;
}
.lpo-support .lpo-fv__message-text--1.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__message-text--1.black {
  color: #333;
}
.lpo-support .lpo-fv__message-text--1.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__message-text--1.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__message-text--1.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__message-text--1.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__message-text--1::before {
  position: relative;
  top: 12px;
  display: inline-block;
  width: 24px;
  height: 24px;
  content: "";
  border-right: 3px solid #333;
  transform: rotate(-45deg);
}
.lpo-support .lpo-fv__message-text--1::after {
  position: relative;
  top: 12px;
  display: inline-block;
  width: 24px;
  height: 24px;
  content: "";
  border-left: 3px solid #333;
  transform: rotate(45deg);
}
.lpo-support .lpo-fv__message-text--bg-white {
  height: 48px;
  padding: 0 4px;
  background-color: #fff;
}
.lpo-support .lpo-fv__message-text--bg-white.md {
  z-index: 1;
  display: inline-block;
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.lpo-support .lpo-fv__message-text--bg-white.md.white {
  color: #fff;
}
.lpo-support .lpo-fv__message-text--bg-white.md.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__message-text--bg-white.md.black {
  color: #333;
}
.lpo-support .lpo-fv__message-text--bg-white.md.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__message-text--bg-white.md.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__message-text--bg-white.md.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__message-text--bg-white.md.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__message-text--bg-white.lg {
  z-index: 1;
  display: inline-block;
  font-size: 5rem;
  font-weight: 700;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.lpo-support .lpo-fv__message-text--bg-white.lg.white {
  color: #fff;
}
.lpo-support .lpo-fv__message-text--bg-white.lg.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__message-text--bg-white.lg.black {
  color: #333;
}
.lpo-support .lpo-fv__message-text--bg-white.lg.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__message-text--bg-white.lg.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__message-text--bg-white.lg.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__message-text--bg-white.lg.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__message-text--md {
  z-index: 1;
  display: inline-block;
  font-size: 2.2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-fv__message-text--md.white {
  color: #fff;
}
.lpo-support .lpo-fv__message-text--md.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__message-text--md.black {
  color: #333;
}
.lpo-support .lpo-fv__message-text--md.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__message-text--md.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__message-text--md.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__message-text--md.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-fv__message-text--lg {
  z-index: 1;
  display: inline-block;
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-fv__message-text--lg.white {
  color: #fff;
}
.lpo-support .lpo-fv__message-text--lg.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-fv__message-text--lg.black {
  color: #333;
}
.lpo-support .lpo-fv__message-text--lg.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-fv__message-text--lg.red {
  color: #fa6b58;
}
.lpo-support .lpo-fv__message-text--lg.green {
  color: #3c8b86;
}
.lpo-support .lpo-fv__message-text--lg.light-green {
  color: #76aaaa;
}
@media (max-width: 1200px) {
  .lpo-support .lpo-fv__container {
    padding-left: 16px;
  }
}
@media (max-width: 1000px) {
  .lpo-support .lpo-fv__container .form-container {
    top: 60px;
  }
}
@media (max-width: 768px) {
  .lpo-support {
    padding-top: 5rem;
  }
  .lpo-support .lpo-fv {
    padding-top: 2rem;
  }
  .lpo-support .lpo-fv::before {
    top: -550px;
    width: 1750px;
  }
  .lpo-support .lpo-fv__container {
    width: 100%;
  }
  .lpo-support .lpo-fv__container::before {
    top: 80px;
    right: unset;
    left: 50%;
    width: 100%;
    height: 100%;
    background-image: url("../../images/lpo/fv-bg-sp.png");
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    transform: translateX(-50%);
  }
  .lpo-support .lpo-fv__container .form-container {
    display: none;
  }
  .lpo-support .lpo-fv__container .fv-title {
    width: 100%;
  }
  .lpo-support .lpo-fv__container .fv-title__catch {
    justify-content: center;
  }
  .lpo-support .lpo-fv__container .fv-title__catch--accent {
    width: 194px;
    height: 66px;
    padding-top: 12px;
    font-size: 1.6rem;
    text-align: center;
    background-image: url("../../images/lpo/fv-catch-accent-bg-sp.svg");
  }
  .lpo-support .lpo-fv__container .fv-title__catch--text-sm {
    font-size: 1.6rem;
  }
  .lpo-support .lpo-fv__container .fv-title__catch--text-md {
    font-size: 3rem;
  }
  .lpo-support .lpo-fv__container .fv-title__catch--text-lg {
    font-size: 4rem;
  }
  .lpo-support .lpo-fv__container .fv-title__catch--text-xl {
    font-size: 7rem;
  }
  .lpo-support .lpo-fv__container .fv-title__catch--text-xxl {
    font-size: 8rem;
  }
  .lpo-support .lpo-fv__message {
    padding-top: 2rem;
  }
}

.lpo-support .lpo-check {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  background-color: #e0f3ee;
}
@media (max-width: 480px) {
  .lpo-support .lpo-check {
    padding: 80px 0 40px;
  }
}
.lpo-support .lpo-check::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 33rem;
  pointer-events: none;
  content: "";
  background-color: #f9f9f9;
  background-image: linear-gradient(to right, #c0c0c0 0.5px, transparent 0.5px), linear-gradient(to bottom, #c0c0c0 0.5px, transparent 0.5px);
  background-size: 3rem 3rem;
}
.lpo-support .lpo-check::after {
  position: absolute;
  bottom: -2rem;
  left: 50%;
  z-index: 2;
  width: 4rem;
  height: 4rem;
  content: "";
  background-color: #e0f3ee;
  transform: translateX(-50%) rotate(135deg) skew(10deg, 10deg);
}
.lpo-support .lpo-check__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1002px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-support .lpo-check__container {
    max-width: 100%;
  }
}
.lpo-support .lpo-check__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-support .lpo-check__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-support .lpo-check__container .lpo-section-title.black {
  color: #333;
}
.lpo-support .lpo-check__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-support .lpo-check__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-support .lpo-check__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-support .lpo-check__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-support .lpo-check__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-support .lpo-check__title {
  display: flex;
  flex-direction: column;
  align-items: center;
  line-height: 1;
}
.lpo-support .lpo-check__check-list {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  gap: 0.6rem;
  row-gap: 7rem;
  justify-content: center;
  width: 100%;
  margin-top: 10rem;
}
.lpo-support .lpo-check__check-list::before {
  position: absolute;
  top: -8rem;
  left: 50%;
  display: block;
  width: 6.3px;
  height: 3.6rem;
  content: "";
  background-image: url("../../images/lpo/case-dot.svg");
  background-repeat: no-repeat;
  background-size: contain;
  transform: translateX(-50%);
}
.lpo-support .lpo-check__check-list .check-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: calc(33% - 0.2rem);
  max-height: 15.8rem;
  padding: 5.6rem 0 4.8rem;
  background-color: #fff;
  border: 1px solid #a0c1bb;
  border-bottom: 1px solid #c0c0c0;
  box-shadow: 0 0 14px 0 rgba(125, 200, 182, 0.4);
}
.lpo-support .lpo-check__check-list .check-item::after {
  position: absolute;
  top: -3.4rem;
  left: 50%;
  width: 6.8rem;
  height: 6.8rem;
  content: "";
  background-image: url("../../images/lpo/case-check-icon.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateX(-50%);
}
.lpo-support .lpo-check__check-list .check-item__text {
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-check__check-list .check-item__text.white {
  color: #fff;
}
.lpo-support .lpo-check__check-list .check-item__text.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-check__check-list .check-item__text.black {
  color: #333;
}
.lpo-support .lpo-check__check-list .check-item__text.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-check__check-list .check-item__text.red {
  color: #fa6b58;
}
.lpo-support .lpo-check__check-list .check-item__text.green {
  color: #3c8b86;
}
.lpo-support .lpo-check__check-list .check-item__text.light-green {
  color: #76aaaa;
}

.lpo-support .lpo-improve {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  padding-bottom: 40px;
  background-color: #e0f3ee;
}
@media (max-width: 480px) {
  .lpo-support .lpo-improve {
    padding: 80px 0 40px;
  }
}
.lpo-support .lpo-improve::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 33rem;
  pointer-events: none;
  content: "";
  background-color: #f9f9f9;
  background-image: linear-gradient(to right, #c0c0c0 0.5px, transparent 0.5px), linear-gradient(to bottom, #c0c0c0 0.5px, transparent 0.5px);
  background-size: 3rem 3rem;
}
.lpo-support .lpo-improve__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 782px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-support .lpo-improve__container {
    max-width: 100%;
  }
}
.lpo-support .lpo-improve__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-support .lpo-improve__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__container .lpo-section-title.black {
  color: #333;
}
.lpo-support .lpo-improve__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-support .lpo-improve__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-support .lpo-improve__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-support .lpo-improve__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-support .lpo-improve__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-support .lpo-improve__container::before {
  position: absolute;
  top: -60px;
  right: 20px;
  z-index: 0;
  width: 522px;
  height: 394px;
  content: "";
  background-image: url("../../images/lpo/lpo-title-arrow-1.svg");
  background-repeat: no-repeat;
  background-position: bottom;
  background-size: 100% 100%;
}
.lpo-support .lpo-improve__title {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: flex-end;
  margin-bottom: 6rem;
}
.lpo-support .lpo-improve__bfaf {
  z-index: 1;
  width: 100%;
}
.lpo-support .lpo-improve__bfaf .tab-wrap {
  display: flex;
  gap: 1.2rem;
  justify-content: center;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: 50%;
  width: 300px;
  height: 80px;
  border-radius: 19px 19px 0 0;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item--left {
  background-color: #e1ede8;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item--right {
  background-color: #fff;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item-text {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item-text.white {
  color: #fff;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item-text.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item-text.black {
  color: #333;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item-text.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item-text.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item-text.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .tab-wrap .tab__item-text.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-improve__bfaf .data-wrap {
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 20px 0;
  border-radius: 26px;
  background: linear-gradient(to right, transparent 50%, #fff 50%), linear-gradient(to bottom, #e1ede8 0%, #fff 70%);
  background-repeat: no-repeat;
  background-position: 0 0, 0 0;
  background-size: 100% 100%, 50% 100%;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  width: 100%;
  max-width: 610px;
  height: 100%;
  padding-bottom: 20px;
  overflow: hidden;
  border: 3px solid #fff;
  border-radius: 16px;
  box-shadow: 0 0 4px 0 #3c8b86;
  background: linear-gradient(to right, transparent 50%, #fff 50%), linear-gradient(to bottom, #e1ede8 0%, #fff 100%);
  background-repeat: no-repeat;
  background-position: 0 0, 0 0;
  background-size: 100% 100%, 50% 100%;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  height: 34px;
  padding-left: 60px;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title::before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 50%;
  height: 100%;
  content: "";
  background-color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title::after {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 0;
  width: 34px;
  height: 34px;
  content: "";
  background: linear-gradient(45deg, #3c8b86 50%, #fff 50%);
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title__number {
  position: absolute;
  top: 50%;
  left: 24px;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  font-size: 2rem;
  font-weight: 700;
  color: #3c8b86;
  background-color: #fff;
  border-radius: 50%;
  transform: translateY(-50%);
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title__text {
  z-index: 2;
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title__text.white {
  color: #fff;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title__text.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title__text.black {
  color: #333;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title__text.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title__text.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title__text.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item-title__text.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-image__1 {
  max-width: 370px;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-image__2 {
  max-width: 370px;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1 {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.white {
  color: #fff;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.black {
  color: #333;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-text__1.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio {
  position: absolute;
  top: 50%;
  left: 43%;
  z-index: 1;
  transform: translateY(-50%);
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg {
  z-index: 1;
  display: inline-block;
  font-size: 4rem;
  font-weight: 700;
  line-height: 1;
  margin-right: -4px;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.white {
  color: #fff;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.black {
  color: #333;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--lg.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.white {
  color: #fff;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.black {
  color: #333;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio__text--sm.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .ratio::before {
  position: absolute;
  top: 0;
  right: -55px;
  z-index: -1;
  width: 88px;
  height: 44px;
  content: "";
  background-image: url("../../images/lpo/lpo-ratio-arrow.svg");
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
  justify-content: center;
  width: 50%;
  padding-top: 22px;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content--left {
  padding-right: 48px;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content--right {
  padding-left: 48px;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 192px;
  height: 38px;
  border-radius: 40px;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user::before {
  position: absolute;
  left: 50%;
  content: "";
  background-repeat: no-repeat;
  background-size: contain;
  transform: translateX(-50%);
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__2-1 {
  background-color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__2-1::before {
  top: -24px;
  width: 62px;
  height: 28px;
  background-image: url("../../images/lpo/lpo-bfaf-2-1.svg");
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__2-2 {
  background-color: #fff;
  border: 3px solid #fa6b58;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__2-2::before {
  top: -51px;
  width: 156px;
  height: 50px;
  background-image: url("../../images/lpo/lpo-bfaf-2-2.svg");
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__3-1 {
  background-color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__3-1::before {
  top: -22px;
  width: 39px;
  height: 23px;
  background-image: url("../../images/lpo/lpo-bfaf-3-1.svg");
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__3-2 {
  background-color: #fff;
  border: 3px solid #fa6b58;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__3-2::before {
  top: -51px;
  width: 90px;
  height: 50px;
  background-image: url("../../images/lpo/lpo-bfaf-3-2.svg");
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text {
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.white {
  color: #fff;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.black {
  color: #333;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content .bfaf-user__text.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text {
  z-index: 1;
  display: inline-block;
  font-size: 1.2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.white {
  color: #fff;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.black {
  color: #333;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__bfaf .data-wrap .data-item .bfaf-content-wrap .bfaf-content__text.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-improve__message {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  margin-top: 24px;
}
.lpo-support .lpo-improve__message::before {
  position: absolute;
  top: 8px;
  left: 50%;
  z-index: 0;
  width: 120%;
  height: 120%;
  content: "";
  background-image: url("../../images/lpo/lpo-bfaf-message-bg.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  transform: translateX(-50%);
}
.lpo-support .lpo-improve__message.no-bg::before {
  display: none;
}
.lpo-support .lpo-improve__message-text--sm {
  z-index: 1;
  display: inline-block;
  font-size: 1.2rem;
  font-weight: 400;
  line-height: 1;
}
.lpo-support .lpo-improve__message-text--sm.white {
  color: #fff;
}
.lpo-support .lpo-improve__message-text--sm.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__message-text--sm.black {
  color: #333;
}
.lpo-support .lpo-improve__message-text--sm.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__message-text--sm.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__message-text--sm.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__message-text--sm.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-improve__message-text--md {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-improve__message-text--md.white {
  color: #fff;
}
.lpo-support .lpo-improve__message-text--md.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__message-text--md.black {
  color: #333;
}
.lpo-support .lpo-improve__message-text--md.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__message-text--md.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__message-text--md.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__message-text--md.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-improve__message-text--lg {
  z-index: 1;
  display: inline-block;
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-improve__message-text--lg.white {
  color: #fff;
}
.lpo-support .lpo-improve__message-text--lg.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-improve__message-text--lg.black {
  color: #333;
}
.lpo-support .lpo-improve__message-text--lg.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-improve__message-text--lg.red {
  color: #fa6b58;
}
.lpo-support .lpo-improve__message-text--lg.green {
  color: #3c8b86;
}
.lpo-support .lpo-improve__message-text--lg.light-green {
  color: #76aaaa;
}

.lpo-support .lpo-service {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  padding-bottom: 10rem;
  background-color: #e0f3ee;
}
@media (max-width: 480px) {
  .lpo-support .lpo-service {
    padding: 80px 0 40px;
  }
}
.lpo-support .lpo-service::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 18rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
  background-image: linear-gradient(to right, #fff, #fff), linear-gradient(to bottom, #fff, #fff);
  background-size: 3rem 3rem;
}
.lpo-support .lpo-service__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 892px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-support .lpo-service__container {
    max-width: 100%;
  }
}
.lpo-support .lpo-service__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-support .lpo-service__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-support .lpo-service__container .lpo-section-title.black {
  color: #333;
}
.lpo-support .lpo-service__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-support .lpo-service__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-support .lpo-service__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-support .lpo-service__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-support .lpo-service__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-support .lpo-service .lpo-section__title {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-bottom: 5rem;
}
.lpo-support .lpo-service .service-items {
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;
  width: 100%;
  padding-top: 4rem;
}
.lpo-support .lpo-service .service-items:last-child {
  margin-bottom: 10rem;
}
.lpo-support .lpo-service .service-items .service-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap {
  display: flex;
  gap: 40px;
  align-items: center;
  width: 100%;
  height: 55px;
  padding: 4px;
  background-color: #fff;
  border-radius: 9px;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap::before {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 47px;
  height: 47px;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  text-align: center;
  background-color: #3ab795;
  border-radius: 9px;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap--1::before {
  content: "01";
}
.lpo-support .lpo-service .service-items .service-item__title-wrap--2::before {
  content: "02";
}
.lpo-support .lpo-service .service-items .service-item__title-wrap--3::before {
  content: "03";
}
.lpo-support .lpo-service .service-items .service-item__title-wrap--4::before {
  content: "04";
}
.lpo-support .lpo-service .service-items .service-item__title-wrap .service-item__title {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 500;
  line-height: 1;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap .service-item__title.white {
  color: #fff;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap .service-item__title.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap .service-item__title.black {
  color: #333;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap .service-item__title.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap .service-item__title.red {
  color: #fa6b58;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap .service-item__title.green {
  color: #3c8b86;
}
.lpo-support .lpo-service .service-items .service-item__title-wrap .service-item__title.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-service .service-items .service-item__description {
  z-index: 1;
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 500;
  line-height: 1;
  padding-left: 60px;
  color: #333;
}
.lpo-support .lpo-service .service-items .service-item__description.white {
  color: #fff;
}
.lpo-support .lpo-service .service-items .service-item__description.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-service .service-items .service-item__description.black {
  color: #333;
}
.lpo-support .lpo-service .service-items .service-item__description.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-service .service-items .service-item__description.red {
  color: #fa6b58;
}
.lpo-support .lpo-service .service-items .service-item__description.green {
  color: #3c8b86;
}
.lpo-support .lpo-service .service-items .service-item__description.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-service .service-items .service-item__image {
  display: flex;
  gap: 22px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 60px;
}
.lpo-support .lpo-service .service-items .service-item__image .service-image-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50%;
  padding: 4px;
  background-color: #fff;
  border: 2px solid #a0c1bb;
  border-radius: 9px;
  box-shadow: 0 0 14px 0 rgba(125, 200, 182, 0.8);
}
.lpo-support .lpo-service .service-items .service-item__image .service-image-wrap--1 {
  width: 100%;
}
.lpo-support .lpo-service .service-items .service-item__image .service-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.lpo-support .lpo-case {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  padding-top: 12rem;
  background-color: #e0f3ee;
}
@media (max-width: 480px) {
  .lpo-support .lpo-case {
    padding: 80px 0 40px;
  }
}
.lpo-support .lpo-case::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 35rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
  background-image: linear-gradient(to right, #fff, #fff), linear-gradient(to bottom, #fff, #fff);
  background-size: 3rem 3rem;
}
.lpo-support .lpo-case__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-support .lpo-case__container {
    max-width: 100%;
  }
}
.lpo-support .lpo-case__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-support .lpo-case__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-support .lpo-case__container .lpo-section-title.black {
  color: #333;
}
.lpo-support .lpo-case__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-support .lpo-case__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-support .lpo-case__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-support .lpo-case__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-support .lpo-case__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-support .lpo-case .lpo-case__title {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-bottom: 10rem;
}
.lpo-support .lpo-case .lpo-case__title::before {
  position: absolute;
  top: -180px;
  right: -300px;
  z-index: 0;
  width: 588px;
  height: 443px;
  content: "";
  background-image: url("../../images/lpo/lpo-title-arrow-2.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.lpo-support .lpo-case__content-wrap {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  width: 100%;
  margin-bottom: 7.2rem;
}
.lpo-support .lpo-case__content-wrap:last-child {
  margin-bottom: 10rem;
}
.lpo-support .lpo-case__content-wrap .case-content {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  width: 100%;
  padding: 4.8rem 11rem;
  background-color: #fff;
  border-radius: 22px;
  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.25);
}
.lpo-support .lpo-case__content-wrap .case-content__title {
  position: absolute;
  top: -30px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 414px;
  height: 60px;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
  background-color: #fff;
  border: 4px solid #3ab795;
  border-radius: 30px;
}
.lpo-support .lpo-case__content-wrap .case-content__title-text {
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1;
  color: #3ab795;
}
.lpo-support .lpo-case__content-wrap .case-content .case-subtitle-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.lpo-support .lpo-case__content-wrap .case-content .case-subtitle-wrap .case-subtitle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 33px;
  padding: 4px 32px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 1.8rem;
  font-weight: 700;
  color: #333;
  background-color: #d9d9d9;
}
.lpo-support .lpo-case__content-wrap .case-content .case-subtitle-wrap .case-subtitle::after {
  position: absolute;
  top: 0;
  right: -33px;
  z-index: 0;
  width: 33px;
  height: 33px;
  content: "";
  background: linear-gradient(125deg, #d9d9d9 50%, #fff 50%);
}
.lpo-support .lpo-case__content-wrap .case-content .case-subtitle-wrap .case-subtitle-text {
  margin-left: 33px;
  font-size: 1.6rem;
  font-weight: 600;
  color: #333;
}
.lpo-support .lpo-case__content-wrap .case-content .description-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: flex-start;
  width: 100%;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-start;
  width: 100%;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__title {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  font-size: 1.6rem;
  font-weight: 600;
  color: #333;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__title::before {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  margin-right: 2rem;
  font-size: 1.8rem;
  font-weight: 500;
  color: #fff;
  background-color: #76aaaa;
  border-radius: 50%;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__title--1::before {
  content: "1";
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__title--2::before {
  content: "2";
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__title--3::before {
  content: "3";
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap {
  position: relative;
  display: flex;
  width: 100%;
  height: 127px;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap::before {
  position: absolute;
  top: 0;
  left: 48.5%;
  width: 100%;
  height: 100%;
  content: "";
  background-image: url("../../images/lpo/lpo-vc-arrow.svg");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateX(-50%);
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item::before {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item.vc-before {
  width: 48.5%;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item.vc-before::before {
  background-image: url("../../images/lpo/lpo-vc-before.svg");
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item.vc-after {
  width: 51.5%;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item.vc-after::before {
  background-image: url("../../images/lpo/lpo-vc-after.svg");
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title {
  z-index: 1;
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 44px;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.white {
  color: #fff;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.black {
  color: #333;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.red {
  color: #fa6b58;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.green {
  color: #3c8b86;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__title.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 85px;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text {
  display: flex;
  gap: 4px;
  align-items: flex-end;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm {
  z-index: 1;
  display: inline-block;
  font-size: 1.8rem;
  font-weight: 500;
  line-height: 1;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.white {
  color: #fff;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.black {
  color: #333;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.red {
  color: #fa6b58;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.green {
  color: #3c8b86;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__sm.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md {
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 500;
  line-height: 1;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.white {
  color: #fff;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.black {
  color: #333;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.red {
  color: #fa6b58;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.green {
  color: #3c8b86;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__md.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg {
  z-index: 1;
  display: inline-block;
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.white {
  color: #fff;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.black {
  color: #333;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.red {
  color: #fa6b58;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.green {
  color: #3c8b86;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__lg.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent {
  z-index: 1;
  display: inline-block;
  font-size: 2rem;
  font-weight: 500;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 4px 12px;
  background-color: #fa6b58;
  border-radius: 16px;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.white {
  color: #fff;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.black {
  color: #333;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.red {
  color: #fa6b58;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.green {
  color: #3c8b86;
}
.lpo-support .lpo-case__content-wrap .case-content .description-item__vc-wrap .vc-item__data-item .vc-data-text__accent.light-green {
  color: #76aaaa;
}

.lpo-support .lpo-flow {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
  padding-top: 12rem;
}
@media (max-width: 480px) {
  .lpo-support .lpo-flow {
    padding: 80px 0 40px;
  }
}
.lpo-support .lpo-flow__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 892px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-support .lpo-flow__container {
    max-width: 100%;
  }
}
.lpo-support .lpo-flow__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-support .lpo-flow__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-support .lpo-flow__container .lpo-section-title.black {
  color: #333;
}
.lpo-support .lpo-flow__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-support .lpo-flow__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-support .lpo-flow__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-support .lpo-flow__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-support .lpo-flow__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-support .lpo-flow .lpo-flow__title {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-bottom: 6rem;
}
.lpo-support .lpo-flow__content {
  display: flex;
  flex-direction: column;
  gap: 32px;
  align-items: center;
  width: 100%;
}
.lpo-support .lpo-flow .flow-step-wrap {
  display: flex;
  flex-direction: column;
  gap: 34px;
  align-items: center;
  width: 100%;
}
.lpo-support .lpo-flow .flow-step {
  position: relative;
  display: flex;
  justify-content: space-between;
  width: 100%;
  min-height: 120px;
  padding: 4px 32px 4px 72px;
  background-color: #fff;
  box-shadow: 0 4px 11px rgba(0, 0, 0, 0.25);
}
.lpo-support .lpo-flow .flow-step::before {
  position: absolute;
  bottom: -10px;
  left: 50%;
  z-index: 0;
  width: 20px;
  height: 20px;
  content: "";
  background-color: #fff;
  transform: translateX(-50%) rotate(45deg) skew(15deg, 15deg);
}
.lpo-support .lpo-flow .flow-step__number {
  position: absolute;
  top: 4px;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 55px;
  height: 55px;
  font-family: "Yu Gothic", "YuGothic", "Hiragino Kaku Gothic ProN", "Hiragino Sans", meiryo, sans-serif;
  font-size: 2.8rem;
  font-weight: 700;
  color: #fff;
  background-color: #3ab795;
}
.lpo-support .lpo-flow .flow-step__description {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  height: 100%;
  padding-right: 32px;
}
.lpo-support .lpo-flow .flow-step__description .flow-title {
  display: flex;
  align-items: center;
  height: 55px;
}
.lpo-support .lpo-flow .flow-step__description .flow-title__text {
  font-size: 2.4rem;
  font-weight: 700;
  line-height: normal;
  color: #3ab795;
}
.lpo-support .lpo-flow .flow-step__description .flow-text {
  display: flex;
}
.lpo-support .lpo-flow .flow-step__description .flow-text__text {
  font-size: 1.6rem;
  font-weight: 400;
  color: #333;
}
.lpo-support .lpo-flow .flow-step .flow-image {
  position: relative;
  right: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  max-height: 100%;
}
.lpo-support .lpo-flow .flow-step .flow-image__image-1 {
  width: 58px;
  height: 73px;
}
.lpo-support .lpo-flow .flow-step .flow-image__image-2 {
  width: 75px;
  height: 84px;
}
.lpo-support .lpo-flow .flow-step .flow-image__image-3 {
  width: 68px;
  height: 86px;
}
.lpo-support .lpo-flow .flow-step .flow-image__image-4 {
  width: 80px;
  height: 83px;
}

.lpo-support .lpo-price {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  padding-bottom: 10rem;
  background-color: #f9f9f9;
}
@media (max-width: 480px) {
  .lpo-support .lpo-price {
    padding: 80px 0 40px;
  }
}
.lpo-support .lpo-price::before {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 21.5rem;
  pointer-events: none;
  content: "";
  background-color: #fff;
}
.lpo-support .lpo-price__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 892px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-support .lpo-price__container {
    max-width: 100%;
  }
}
.lpo-support .lpo-price__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-support .lpo-price__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-support .lpo-price__container .lpo-section-title.black {
  color: #333;
}
.lpo-support .lpo-price__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-support .lpo-price__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-support .lpo-price__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-support .lpo-price__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-support .lpo-price__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-support .lpo-price .lpo-section__title {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-bottom: 5rem;
}
.lpo-support .lpo-price__title {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 76px;
  margin-bottom: 9.5rem;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
  background: linear-gradient(to right, #3ab795 30%, #fff 30%);
  border: 4px solid #3ab795;
  border-radius: 37px;
}
.lpo-support .lpo-price__title-text {
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1;
  text-align: center;
}
.lpo-support .lpo-price__title-text--small {
  z-index: 1;
  display: inline-block;
  font-size: 2.2rem;
  font-weight: 700;
  line-height: 1;
}
.lpo-support .lpo-price__title-text--small.white {
  color: #fff;
}
.lpo-support .lpo-price__title-text--small.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-price__title-text--small.black {
  color: #333;
}
.lpo-support .lpo-price__title-text--small.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-price__title-text--small.red {
  color: #fa6b58;
}
.lpo-support .lpo-price__title-text--small.green {
  color: #3c8b86;
}
.lpo-support .lpo-price__title-text--small.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-price__title-left {
  width: 30%;
  color: #fff;
}
.lpo-support .lpo-price__title-right {
  width: 70%;
  color: #5f6061;
}
.lpo-support .lpo-price__content-wrap {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  width: 100%;
  height: 487px;
  padding: 0 5.5rem;
  margin-bottom: 4.6rem;
  background-color: #e0f3ee;
  border: 4px solid #76aaaa;
  border-radius: 61px;
}
.lpo-support .lpo-price__content-wrap::after {
  position: absolute;
  bottom: -23px;
  left: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 398px;
  height: 46px;
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1;
  color: #5f6061;
  text-align: center;
  content: "1ターム";
  background-color: #fff;
  border: 3px solid #76aaaa;
  transform: translateX(-50%);
}
.lpo-support .lpo-price__content-wrap .price-items {
  position: relative;
  top: -48px;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.lpo-support .lpo-price__content-wrap .price-items-row {
  display: flex;
  row-gap: 8px;
  width: 100%;
  height: 266px;
}
.lpo-support .lpo-price__content-wrap .price-items-row--1 {
  flex-direction: row;
}
.lpo-support .lpo-price__content-wrap .price-items-row--2 {
  flex-direction: row-reverse;
  margin-top: -22px;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 55%;
  background-repeat: no-repeat;
  background-position: center;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item::before {
  position: absolute;
  content: "";
  background-repeat: no-repeat;
  background-position: center;
  background-size: 105% 105%;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item--1 {
  z-index: 4;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item--1::before {
  width: 396px;
  height: 238px;
  background-image: url("../../images/lpo/lpo-price-bg-1.svg");
}
.lpo-support .lpo-price__content-wrap .price-items .price-item--2 {
  z-index: 3;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item--2::before {
  width: 369px;
  height: 266px;
  background-image: url("../../images/lpo/lpo-price-bg-2.svg");
}
.lpo-support .lpo-price__content-wrap .price-items .price-item--3 {
  z-index: 2;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item--3::before {
  right: 0;
  width: 400px;
  height: 238px;
  background-image: url("../../images/lpo/lpo-price-bg-3.svg");
}
.lpo-support .lpo-price__content-wrap .price-items .price-item--4 {
  z-index: 1;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item--4::before {
  width: 370px;
  height: 238px;
  background-image: url("../../images/lpo/lpo-price-bg-4.svg");
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__title {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 7.3rem;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__title.white {
  color: #fff;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__title.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__title.black {
  color: #333;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__title.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__title.red {
  color: #fa6b58;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__title.green {
  color: #3c8b86;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__title.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 15.5rem;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__data-text {
  z-index: 1;
  display: inline-block;
  font-size: 3rem;
  font-weight: 700;
  line-height: 1;
  line-height: 1.5;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__data-text.white {
  color: #fff;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__data-text.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__data-text.black {
  color: #333;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__data-text.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__data-text.red {
  color: #fa6b58;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__data-text.green {
  color: #3c8b86;
}
.lpo-support .lpo-price__content-wrap .price-items .price-item__data-text.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-price .lpo-price__note {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.lpo-support .lpo-price .lpo-price__note .price-text--sm {
  z-index: 1;
  display: inline-block;
  font-size: 1.2rem;
  font-weight: 400;
  line-height: 1;
  width: 100%;
  line-height: 1.5;
  color: #5f6061;
  text-align: center;
}
.lpo-support .lpo-price .lpo-price__note .price-text--sm.white {
  color: #fff;
}
.lpo-support .lpo-price .lpo-price__note .price-text--sm.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-price .lpo-price__note .price-text--sm.black {
  color: #333;
}
.lpo-support .lpo-price .lpo-price__note .price-text--sm.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-price .lpo-price__note .price-text--sm.red {
  color: #fa6b58;
}
.lpo-support .lpo-price .lpo-price__note .price-text--sm.green {
  color: #3c8b86;
}
.lpo-support .lpo-price .lpo-price__note .price-text--sm.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-price .lpo-price__note .price-text--md {
  z-index: 1;
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 700;
  line-height: 1;
  width: 100%;
  line-height: 1.6;
  color: #333;
  text-align: center;
}
.lpo-support .lpo-price .lpo-price__note .price-text--md.white {
  color: #fff;
}
.lpo-support .lpo-price .lpo-price__note .price-text--md.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-price .lpo-price__note .price-text--md.black {
  color: #333;
}
.lpo-support .lpo-price .lpo-price__note .price-text--md.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-price .lpo-price__note .price-text--md.red {
  color: #fa6b58;
}
.lpo-support .lpo-price .lpo-price__note .price-text--md.green {
  color: #3c8b86;
}
.lpo-support .lpo-price .lpo-price__note .price-text--md.light-green {
  color: #76aaaa;
}

.lpo-support .lpo-faq {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
}
@media (max-width: 480px) {
  .lpo-support .lpo-faq {
    padding: 80px 0 40px;
  }
}
.lpo-support .lpo-faq__container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1002px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .lpo-support .lpo-faq__container {
    max-width: 100%;
  }
}
.lpo-support .lpo-faq__container .lpo-section-title {
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.lpo-support .lpo-faq__container .lpo-section-title.red {
  color: #fa6b58;
}
.lpo-support .lpo-faq__container .lpo-section-title.black {
  color: #333;
}
.lpo-support .lpo-faq__container .lpo-section-title.medium {
  font-weight: 500;
}
.lpo-support .lpo-faq__container .lpo-section-title.xs {
  font-size: 2rem;
}
.lpo-support .lpo-faq__container .lpo-section-title.sm {
  font-size: 2.4rem;
}
.lpo-support .lpo-faq__container .lpo-section-title.md {
  font-size: 4rem;
}
.lpo-support .lpo-faq__container .lpo-section-title.lg {
  font-size: 5rem;
}
.lpo-support .lpo-faq .lpo-section__title {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  margin-bottom: 5rem;
}
.lpo-support .lpo-faq .faq__list {
  display: flex;
  flex-direction: column;
}
.lpo-support .lpo-faq .faq__list .faq-item {
  display: flex;
  flex-direction: column;
  gap: 1.6rem;
  padding: 1.6rem 0;
  border-bottom: 1px dashed #8b8b8b;
}
.lpo-support .lpo-faq .faq__list .faq-item .question {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.lpo-support .lpo-faq .faq__list .faq-item .question::before {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 46px;
  margin-right: 2.4rem;
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  content: "Q";
  background-color: #3ab795;
  border-radius: 7px;
}
.lpo-support .lpo-faq .faq__list .faq-item .question__text {
  z-index: 1;
  display: inline-block;
  font-size: 2.4rem;
  font-weight: 700;
  line-height: 1;
  color: #5f6061;
}
.lpo-support .lpo-faq .faq__list .faq-item .question__text.white {
  color: #fff;
}
.lpo-support .lpo-faq .faq__list .faq-item .question__text.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-faq .faq__list .faq-item .question__text.black {
  color: #333;
}
.lpo-support .lpo-faq .faq__list .faq-item .question__text.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-faq .faq__list .faq-item .question__text.red {
  color: #fa6b58;
}
.lpo-support .lpo-faq .faq__list .faq-item .question__text.green {
  color: #3c8b86;
}
.lpo-support .lpo-faq .faq__list .faq-item .question__text.light-green {
  color: #76aaaa;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding-left: 10rem;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer::before {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 46px;
  margin-right: 2.4rem;
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  content: "A";
  background-color: #8fbdc3;
  border-radius: 7px;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer__text {
  z-index: 1;
  display: inline-block;
  font-size: 1.6rem;
  font-weight: 400;
  line-height: 1;
  color: #333;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer__text.white {
  color: #fff;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer__text.sumikuro {
  color: #5f6061;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer__text.black {
  color: #333;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer__text.gray {
  color: #8b8b8b;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer__text.red {
  color: #fa6b58;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer__text.green {
  color: #3c8b86;
}
.lpo-support .lpo-faq .faq__list .faq-item .answer__text.light-green {
  color: #76aaaa;
}

/*# sourceMappingURL=lpo.css.map */
