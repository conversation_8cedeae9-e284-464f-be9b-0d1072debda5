// ==================================================
// FVセクション
// ==================================================
.lpo-support {
  .lpo-fv {
    @include lpo-section;

    position: relative;
    z-index: 0;
    display: flex;
    flex-direction: column;
    padding-bottom: 6rem;
    overflow: hidden;
    background-color: $lpo-color-bg-light-mint;
    background-image: url('../../images/lpo/fv-bg-dot.svg');
    background-repeat: repeat;
    background-position: center;
    background-size: 8px 8px;

    &::before {
      position: absolute;
      top: -450px;
      left: 50%;
      width: 2523px;
      height: 1246px;
      content: '';
      background-color: $lpo-color-bg-light-gray;
      border-radius: 50%;
      transform: translateX(-50%);
    }

    &__container {
      @include lpo-container(1600px);

      position: relative;
      display: flex;
      align-items: flex-start;
      justify-content: flex-start;
      padding-left: 250px;

      &::before {
        position: absolute;
        top: -50px;
        right: 0;
        width: 100%;
        height: 100%;
        content: '';
        background-image: url('../../images/lpo/fv-bg-pc.png');
        background-repeat: no-repeat;
        background-position: center;
      }

      .fv-title {
        z-index: 1;
        min-height: 685px;

        &__catch {
          display: flex;
          gap: 8px;
          align-items: center;

          &--accent {
            @include lpo-text(2.5rem, $lpo-font-weight-bold);

            display: flex;
            align-items: center;
            justify-content: center;
            width: 454px;
            height: 55px;
            padding-bottom: 8px;
            color: $lpo-color-mint;
            background-image: url('../../images/lpo/fv-catch-accent-bg.svg');
            background-repeat: no-repeat;
            background-position: center;
            background-size: cover;
          }

          &--text {
            &-sm {
              @include lpo-text(2.5rem, $lpo-font-weight-bold);
            }

            &-md {
              @include lpo-text(4rem, $lpo-font-weight-bold);
            }

            &-lg {
              @include lpo-text(6rem, $lpo-font-weight-bold);
            }

            &-xl {
              @include lpo-text(10rem, $lpo-font-weight-bold);
            }

            &-xxl {
              @include lpo-text(13rem, $lpo-font-weight-bold);
            }

            color: $lpo-color-text-black;
          }
        }
      }

      .form-container {
        position: absolute;
        top: 0;
        right: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 375px;
        height: 696px;
        font-size: 2.2rem;
        background-color: $lpo-color-bg-gray;
      }
    }

    &__message {
      display: flex;
      flex-direction: column;
      gap: 12px;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding-top: 9rem;
      margin-bottom: 40px;

      &-text {
        display: flex;
        align-items: flex-end;

        &--1 {
          @include lpo-text(2.4rem, $lpo-font-weight-bold);

          &::before {
            position: relative;
            top: 12px;
            display: inline-block;
            width: 24px;
            height: 24px;
            content: '';
            border-right: 3px solid $lpo-color-text-black;
            transform: rotate(-45deg);
          }

          &::after {
            position: relative;
            top: 12px;
            display: inline-block;
            width: 24px;
            height: 24px;
            content: '';
            border-left: 3px solid $lpo-color-text-black;
            transform: rotate(45deg);
          }
        }

        &--bg-white {
          height: 48px;
          padding: 0 4px;
          background-color: #fff;

          &.md {
            @include lpo-text(3rem, $lpo-font-weight-bold);

            display: flex;
            align-items: center;
            justify-content: center;
          }

          &.lg {
            @include lpo-text(5rem, $lpo-font-weight-bold);

            display: flex;
            align-items: center;
            justify-content: center;
          }
        }

        &--md {
          @include lpo-text(2.2rem, $lpo-font-weight-bold);
        }

        &--lg {
          @include lpo-text(4rem, $lpo-font-weight-bold);
        }
      }
    }
  }

  @include lpo-mq(xl) {
    .lpo-fv {
      &__container {
        padding-left: 16px;
      }
    }
  }

  @include lpo-mq(lg) {
    .lpo-fv {
      &__container {
        .form-container {
          top: 60px;
        }
      }
    }
  }

  @include lpo-mq(md) {
    padding-top: 5rem;

    .lpo-fv {
      padding-top: 2rem;

      &::before {
        top: -550px;
        width: 1750px;
      }

      &__container {
        width: 100%;

        &::before {
          top: 80px;
          right: unset;
          left: 50%;
          width: 100%;
          height: 100%;
          background-image: url('../../images/lpo/fv-bg-sp.png');
          background-repeat: no-repeat;
          background-position: center;
          background-size: contain;
          transform: translateX(-50%);
        }

        .form-container {
          display: none;
        }

        .fv-title {
          width: 100%;

          &__catch {
            justify-content: center;

            &--accent {
              width: 194px;
              height: 66px;
              padding-top: 12px;
              font-size: 1.6rem;
              text-align: center;
              background-image: url('../../images/lpo/fv-catch-accent-bg-sp.svg');
            }

            &--text {
              &-sm {
                font-size: 1.6rem;
              }

              &-md {
                font-size: 3rem;
              }

              &-lg {
                font-size: 4rem;
              }

              &-xl {
                font-size: 7rem;
              }

              &-xxl {
                font-size: 8rem;
              }
            }
          }
        }
      }

      &__message {
        padding-top: 2rem;
      }
    }
  }
}
