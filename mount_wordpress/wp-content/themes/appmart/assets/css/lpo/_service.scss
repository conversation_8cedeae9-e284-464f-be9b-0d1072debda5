// ==================================================
// サービス
// ==================================================
.lpo-support {
  .lpo-service {
    @include lpo-section;

    position: relative;
    padding-bottom: 10rem;
    background-color: $lpo-color-bg-light-grean;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 18rem;
      pointer-events: none;
      content: '';
      background-color: #fff;
      background-image:
        linear-gradient(to right, #fff, #fff), linear-gradient(to bottom, #fff, #fff);
      background-size: 3rem 3rem;
    }

    &__container {
      @include lpo-container(860px);
    }

    .lpo-section__title {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      margin-bottom: 5rem;
    }

    .service-items {
      display: flex;
      flex-direction: column;
      gap: 40px;
      align-items: center;
      width: 100%;
      padding-top: 4rem;

      &:last-child {
        margin-bottom: 10rem;
      }

      .service-item {
        display: flex;
        flex-direction: column;
        gap: 12px;
        width: 100%;

        &__title-wrap {
          display: flex;
          gap: 40px;
          align-items: center;
          width: 100%;
          height: 55px;
          padding: 4px;
          background-color: #fff;
          border-radius: 9px;

          &::before {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 47px;
            height: 47px;
            font-size: 2.4rem;
            font-weight: $lpo-font-weight-bold;
            line-height: 1;
            color: #fff;
            text-align: center;
            background-color: $lpo-color-mint;
            border-radius: 9px;
          }

          &--1 {
            &::before {
              content: '01';
            }
          }

          &--2 {
            &::before {
              content: '02';
            }
          }

          &--3 {
            &::before {
              content: '03';
            }
          }

          &--4 {
            &::before {
              content: '04';
            }
          }

          .service-item__title {
            @include lpo-text(2.4rem, $lpo-font-weight-medium);
          }
        }

        &__description {
          @include lpo-text(1.6rem, $lpo-font-weight-medium);

          padding-left: 60px;
          color: $lpo-color-text-black;
        }

        &__image {
          display: flex;
          gap: 22px;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 100%;
          padding: 0 60px;

          .service-image-wrap {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50%;
            padding: 4px;
            background-color: #fff;
            border: 2px solid $lpo-color-border-green;
            border-radius: 9px;
            box-shadow: 0 0 14px 0 rgb(125 200 182 / 80%);

            &--1 {
              width: 100%;
            }
          }

          .service-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }
}
