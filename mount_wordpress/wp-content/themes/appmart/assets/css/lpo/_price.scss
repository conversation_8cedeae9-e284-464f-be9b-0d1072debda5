// ==================================================
// Price
// ==================================================
.lpo-support {
  .lpo-price {
    @include lpo-section;

    position: relative;
    padding-bottom: 10rem;
    background-color: $lpo-color-bg-light-gray;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 21.5rem;
      pointer-events: none;
      content: '';
      background-color: #fff;
    }

    &__container {
      @include lpo-container(860px);
    }

    .lpo-section__title {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      margin-bottom: 5rem;
    }

    &__title {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 76px;
      margin-bottom: 9.5rem;
      font-size: 2.4rem;
      font-weight: $lpo-font-weight-bold;
      line-height: 1;
      background: linear-gradient(to right, $lpo-color-mint 30%, #fff 30%);
      border: 4px solid $lpo-color-mint;
      border-radius: 37px;

      &-text {
        font-size: 2.8rem;
        font-weight: $lpo-font-weight-bold;
        line-height: 1;
        text-align: center;

        &--small {
          @include lpo-text(2.2rem, $lpo-font-weight-bold);
        }
      }

      &-left {
        width: 30%;
        color: #fff;
      }

      &-right {
        width: 70%;
        color: $lpo-color-sumikuro;
      }
    }

    &__content-wrap {
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      width: 100%;
      height: 487px;
      padding: 0 5.5rem;
      margin-bottom: 4.6rem;
      background-color: $lpo-color-bg-light-grean;
      border: 4px solid $lpo-color-text-light-green;
      border-radius: 61px;

      &::after {
        position: absolute;
        bottom: -23px;
        left: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 398px;
        height: 46px;
        font-size: 2.8rem;
        font-weight: $lpo-font-weight-bold;
        line-height: 1;
        color: $lpo-color-sumikuro;
        text-align: center;
        content: '1ターム';
        background-color: #fff;
        border: 3px solid $lpo-color-text-light-green;
        transform: translateX(-50%);
      }

      .price-items {
        position: relative;
        top: -48px;
        display: flex;
        flex-direction: column;
        width: 100%;

        &-row {
          display: flex;
          row-gap: 8px;
          width: 100%;
          height: 266px;

          &--1 {
            flex-direction: row;
          }

          &--2 {
            flex-direction: row-reverse;
            margin-top: -22px;
          }
        }

        .price-item {
          position: relative;
          display: flex;
          flex-direction: column;
          width: 55%;
          background-repeat: no-repeat;
          background-position: center;

          &::before {
            position: absolute;
            content: '';
            background-repeat: no-repeat;
            background-position: center;
            background-size: 105% 105%;
          }

          &--1 {
            z-index: 4;

            &::before {
              width: 396px;
              height: 238px;
              background-image: url('../../images/lpo/lpo-price-bg-1.svg');
            }
          }

          &--2 {
            z-index: 3;

            &::before {
              width: 369px;
              height: 266px;
              background-image: url('../../images/lpo/lpo-price-bg-2.svg');
            }
          }

          &--3 {
            z-index: 2;

            &::before {
              right: 0;
              width: 400px;
              height: 238px;
              background-image: url('../../images/lpo/lpo-price-bg-3.svg');
            }
          }

          &--4 {
            z-index: 1;

            &::before {
              width: 370px;
              height: 238px;
              background-image: url('../../images/lpo/lpo-price-bg-4.svg');
            }
          }

          &__title {
            @include lpo-text(2.4rem, $lpo-font-weight-bold);

            display: flex;
            flex-shrink: 0;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 7.3rem;
          }

          &__data {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 15.5rem;

            &-text {
              @include lpo-text(3rem, $lpo-font-weight-bold);

              line-height: 1.5;
            }
          }
        }
      }
    }

    .lpo-price__note {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;

      .price-text {
        &--sm {
          @include lpo-text(1.2rem, $lpo-font-weight-regular);

          width: 100%;
          line-height: 1.5;
          color: $lpo-color-sumikuro;
          text-align: center;
        }

        &--md {
          @include lpo-text(1.6rem, $lpo-font-weight-bold);

          width: 100%;
          line-height: 1.6;
          color: $lpo-color-text-black;
          text-align: center;
        }
      }
    }
  }
}
