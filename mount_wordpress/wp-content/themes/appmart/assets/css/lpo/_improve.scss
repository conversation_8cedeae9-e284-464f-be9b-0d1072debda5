// ==================================================
// 改善イメージ
// ==================================================
.lpo-support {
  .lpo-improve {
    @include lpo-section;

    position: relative;
    padding-bottom: 40px;
    background-color: $lpo-color-bg-light-grean;

    &::before {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      height: 33rem;
      pointer-events: none;
      content: '';
      background-color: $lpo-color-bg-light-gray;
      background-image:
        linear-gradient(to right, $lpo-color-bg-grid-line 0.5px, transparent 0.5px),
        linear-gradient(to bottom, $lpo-color-bg-grid-line 0.5px, transparent 0.5px);
      background-size: 3rem 3rem;
    }

    &__container {
      @include lpo-container(750px);

      &::before {
        position: absolute;
        top: -60px;
        right: 20px;
        z-index: 0;
        width: 522px;
        height: 394px;
        content: '';
        background-image: url('../../images/lpo/lpo-title-arrow-1.svg');
        background-repeat: no-repeat;
        background-position: bottom;
        background-size: 100% 100%;
      }
    }

    &__title {
      position: relative;
      z-index: 1;
      display: flex;
      align-items: flex-end;
      margin-bottom: 6rem;
    }

    &__bfaf {
      z-index: 1;
      width: 100%;

      .tab-wrap {
        display: flex;
        gap: 1.2rem;
        justify-content: center;

        .tab__item {
          display: flex;
          flex-direction: column;
          gap: 8px;
          align-items: center;
          justify-content: center;
          width: 50%;
          width: 300px;
          height: 80px;
          border-radius: 19px 19px 0 0;

          &--left {
            background-color: $lpo-color-mosgreen;
          }

          &--right {
            background-color: #fff;
          }

          &-text {
            @include lpo-text(2.4rem, $lpo-font-weight-bold);
          }
        }
      }

      .data-wrap {
        display: flex;
        flex-direction: column;
        gap: 1.2rem;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 20px 0;
        border-radius: 26px;

        @include lpo-section-bg-gradient($lpo-color-bg-grean, #fff, 0%, 70%);

        .data-item {
          display: flex;
          flex-direction: column;
          gap: 16px;
          align-items: center;
          width: 100%;
          max-width: 610px;
          height: 100%;
          padding-bottom: 20px;
          overflow: hidden;
          border: 3px solid #fff;
          border-radius: 16px;
          box-shadow: 0 0 4px 0 $lpo-color-text-green;

          @include lpo-section-bg-gradient;

          &-title {
            position: relative;
            z-index: 1;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 34px;
            padding-left: 60px;

            &::before {
              position: absolute;
              top: 0;
              left: 0;
              z-index: 0;
              width: 50%;
              height: 100%;
              content: '';
              background-color: $lpo-color-text-green;
            }

            &::after {
              position: absolute;
              top: 0;
              left: 50%;
              z-index: 0;
              width: 34px;
              height: 34px;
              content: '';
              background: linear-gradient(45deg, $lpo-color-text-green 50%, #fff 50%);
            }

            &__number {
              position: absolute;
              top: 50%;
              left: 24px;
              z-index: 2;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 24px;
              height: 24px;
              font-size: 2rem;
              font-weight: $lpo-font-weight-bold;
              color: $lpo-color-text-green;
              background-color: #fff;
              border-radius: 50%;
              transform: translateY(-50%);
            }

            &__text {
              z-index: 2;

              @include lpo-text(2rem, $lpo-font-weight-bold);
            }
          }

          .bfaf-image {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;

            &__1 {
              max-width: 370px;
            }

            &__2 {
              max-width: 370px;
            }
          }

          .bfaf-text {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;

            &__1 {
              @include lpo-text(2.4rem, $lpo-font-weight-bold);
            }
          }

          .bfaf-content-wrap {
            position: relative;
            display: flex;
            width: 100%;
            height: 100%;

            .ratio {
              &__text {
                &--lg {
                  @include lpo-text(4rem, $lpo-font-weight-bold);

                  margin-right: -4px;
                }

                &--sm {
                  @include lpo-text(2.4rem, $lpo-font-weight-bold);
                }
              }

              position: absolute;
              top: 50%;
              left: 43%;
              z-index: 1;
              transform: translateY(-50%);

              &::before {
                position: absolute;
                top: 0;
                right: -55px;
                z-index: -1;
                width: 88px;
                height: 44px;
                content: '';
                background-image: url('../../images/lpo/lpo-ratio-arrow.svg');
              }
            }

            .bfaf-content {
              display: flex;
              flex-direction: column;
              gap: 4px;
              align-items: center;
              justify-content: center;
              width: 50%;
              padding-top: 22px;

              &--left {
                padding-right: 48px;
              }

              &--right {
                padding-left: 48px;
              }

              .bfaf-user {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 192px;
                height: 38px;
                border-radius: 40px;

                &::before {
                  position: absolute;
                  left: 50%;
                  content: '';
                  background-repeat: no-repeat;
                  background-size: contain;
                  transform: translateX(-50%);
                }

                &__2-1 {
                  background-color: $lpo-color-text-green;

                  &::before {
                    top: -24px;
                    width: 62px;
                    height: 28px;
                    background-image: url('../../images/lpo/lpo-bfaf-2-1.svg');
                  }
                }

                &__2-2 {
                  background-color: #fff;
                  border: 3px solid $lpo-color-text-red;

                  &::before {
                    top: -51px;
                    width: 156px;
                    height: 50px;
                    background-image: url('../../images/lpo/lpo-bfaf-2-2.svg');
                  }
                }

                &__3-1 {
                  background-color: $lpo-color-text-green;

                  &::before {
                    top: -22px;
                    width: 39px;
                    height: 23px;
                    background-image: url('../../images/lpo/lpo-bfaf-3-1.svg');
                  }
                }

                &__3-2 {
                  background-color: #fff;
                  border: 3px solid $lpo-color-text-red;

                  &::before {
                    top: -51px;
                    width: 90px;
                    height: 50px;
                    background-image: url('../../images/lpo/lpo-bfaf-3-2.svg');
                  }
                }

                &__text {
                  @include lpo-text(2rem, $lpo-font-weight-bold);
                }
              }

              &__text {
                @include lpo-text(1.2rem, $lpo-font-weight-bold);
              }
            }
          }
        }
      }
    }

    &__message {
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 8px;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      margin-top: 24px;

      &::before {
        position: absolute;
        top: 8px;
        left: 50%;
        z-index: 0;
        width: 120%;
        height: 120%;
        content: '';
        background-image: url('../../images/lpo/lpo-bfaf-message-bg.svg');
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% 100%;
        transform: translateX(-50%);
      }

      &.no-bg {
        &::before {
          display: none;
        }
      }

      &-text {
        &--sm {
          @include lpo-text(1.2rem, $lpo-font-weight-regular);
        }

        &--md {
          @include lpo-text(2.4rem, $lpo-font-weight-bold);
        }

        &--lg {
          @include lpo-text(3rem, $lpo-font-weight-bold);
        }
      }
    }
  }
}
