// ==================================================
// mixin定義
// ==================================================
// ブレークポイント
$lpo-break-point-xl: 1200px;
$lpo-break-point-lg: 1000px;
$lpo-break-point-md: 768px;
$lpo-break-point-sm: 480px;

// レスポンシブブレークポイント用mixin
@mixin lpo-mq($breakpoint) {
  @if $breakpoint == 'xl' {
    @media (max-width: #{$lpo-break-point-xl}) {
      @content;
    }
  } @else if $breakpoint == 'lg' {
    @media (max-width: #{$lpo-break-point-lg}) {
      @content;
    }
  } @else if $breakpoint == 'md' {
    @media (max-width: #{$lpo-break-point-md}) {
      @content;
    }
  } @else if $breakpoint == 'sm' {
    @media (max-width: #{$lpo-break-point-sm}) {
      @content;
    }
  }
}

// モバイル用メディアクエリ
@mixin lpo-mobile {
  @media (max-width: #{$lpo-break-point-md}) {
    @content;
  }
}

// セクション
@mixin lpo-section($bg-color: #fff) {
  width: 100%;
  padding: 60px 0 100px;
  background-color: $bg-color;

  @include lpo-mq(sm) {
    padding: 80px 0 40px;
  }
}

// セクションコンテナ
@mixin lpo-container($max-width: 970px) {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: $max-width + 32px; // $max-width + padding(16px × 2)
  padding: 0 16px;
  margin: 0 auto;

  @include lpo-mq(md) {
    max-width: 100%;
  }

  // セクションタイトル
  .lpo-section-title {
    font-weight: $lpo-font-weight-bold;
    line-height: 1;
    color: $lpo-color-text-black;

    &.red {
      color: $lpo-color-text-red;
    }

    &.black {
      color: $lpo-color-text-black;
    }

    &.medium {
      font-weight: $lpo-font-weight-medium;
    }

    &.xs {
      font-size: 2rem;
    }

    &.sm {
      font-size: 2.4rem;
    }

    &.md {
      font-size: 4rem;
    }

    &.lg {
      font-size: 5rem;
    }
  }
}

// テキスト共通
@mixin lpo-text($size: 2rem, $font-weight: $lpo-font-weight-bold) {
  z-index: 1;
  display: inline-block;
  font-size: $size;
  font-weight: $font-weight;
  line-height: 1;

  &.white {
    color: #fff;
  }

  &.sumikuro {
    color: $lpo-color-sumikuro;
  }

  &.black {
    color: $lpo-color-text-black;
  }

  &.gray {
    color: $lpo-color-text-gray;
  }

  &.red {
    color: $lpo-color-text-red;
  }

  &.green {
    color: $lpo-color-text-green;
  }

  &.light-green {
    color: $lpo-color-text-light-green;
  }
}

@mixin lpo-section-bg-gradient(
  $color-1: $lpo-color-bg-grean,
  $color-2: #fff,
  $top: 0%,
  $bottom: 100%
) {
  background:
    linear-gradient(to right, transparent 50%, $color-2 50%),
    linear-gradient(to bottom, $color-1 $top, #fff $bottom);
  background-repeat: no-repeat;
  background-position:
    0 0,
    0 0;
  background-size:
    100% 100%,
    50% 100%;
}
