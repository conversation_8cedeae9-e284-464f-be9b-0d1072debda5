// ////////////////////////////
// LPOフロー
// ////////////////////////////
.lpo-support {
  .lpo-flow {
    @include lpo-section($lpo-color-bg-light-gray);

    padding-top: 12rem;

    &__container {
      @include lpo-container(860px);
    }

    .lpo-flow__title {
      position: relative;
      z-index: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      margin-bottom: 6rem;
    }

    &__content {
      display: flex;
      flex-direction: column;
      gap: 32px;
      align-items: center;
      width: 100%;
    }

    .flow-step-wrap {
      display: flex;
      flex-direction: column;
      gap: 34px;
      align-items: center;
      width: 100%;
    }

    .flow-step {
      position: relative;
      display: flex;
      justify-content: space-between;
      width: 100%;
      min-height: 120px;
      padding: 4px 32px 4px 72px;
      background-color: #fff;
      box-shadow: 0 4px 11px rgb(0 0 0 / 25%);

      &::before {
        position: absolute;
        bottom: -10px;
        left: 50%;
        z-index: 0;
        width: 20px;
        height: 20px;
        content: '';
        background-color: #fff;

        // box-shadow: 0 4px 11px rgb(0 0 0 / 25%);
        transform: translateX(-50%) rotate(45deg) skew(15deg, 15deg);
      }

      &__number {
        position: absolute;
        top: 4px;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 55px;
        height: 55px;
        font-family: $lpo-font-family-yu;
        font-size: 2.8rem;
        font-weight: $lpo-font-weight-bold;
        color: #fff;
        background-color: $lpo-color-mint;
      }

      &__description {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: flex-start;
        height: 100%;
        padding-right: 32px;

        .flow-title {
          display: flex;
          align-items: center;
          height: 55px;

          &__text {
            font-size: 2.4rem;
            font-weight: $lpo-font-weight-bold;
            line-height: normal;
            color: $lpo-color-mint;
          }
        }

        .flow-text {
          display: flex;

          &__text {
            font-size: 1.6rem;
            font-weight: $lpo-font-weight-regular;
            color: $lpo-color-text-black;
          }
        }
      }

      .flow-image {
        position: relative;
        right: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        max-height: 100%;

        &__image {
          &-1 {
            width: 58px;
            height: 73px;
          }

          &-2 {
            width: 75px;
            height: 84px;
          }

          &-3 {
            width: 68px;
            height: 86px;
          }

          &-4 {
            width: 80px;
            height: 83px;
          }
        }
      }
    }
  }
}
