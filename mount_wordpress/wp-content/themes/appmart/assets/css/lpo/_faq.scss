// ////////////////////////////
// よくある質問 (FAQ)
// ////////////////////////////
.lpo-support {
  .lpo-faq {
    @include lpo-section;

    &__container {
      @include lpo-container(970px);
    }

    .lpo-section__title {
      display: flex;
      flex-direction: column;
      gap: 16px;
      align-items: center;
      margin-bottom: 5rem;
    }

    .faq {
      &__list {
        display: flex;
        flex-direction: column;

        .faq-item {
          display: flex;
          flex-direction: column;
          gap: 1.6rem;
          padding: 1.6rem 0;
          border-bottom: 1px dashed $lpo-color-text-gray;

          .question {
            display: flex;
            align-items: center;
            justify-content: flex-start;

            &::before {
              display: flex;
              flex-shrink: 0;
              align-items: center;
              justify-content: center;
              width: 46px;
              height: 46px;
              margin-right: 2.4rem;
              font-size: 2.8rem;
              font-weight: $lpo-font-weight-bold;
              line-height: 1;
              color: #fff;
              content: 'Q';
              background-color: $lpo-color-mint;
              border-radius: 7px;
            }

            &__text {
              @include lpo-text(2.4rem, $lpo-font-weight-bold);

              color: $lpo-color-sumikuro;
            }
          }

          .answer {
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;
            padding-left: 10rem;

            &::before {
              display: flex;
              flex-shrink: 0;
              align-items: center;
              justify-content: center;
              width: 46px;
              height: 46px;
              margin-right: 2.4rem;
              font-size: 2.8rem;
              font-weight: $lpo-font-weight-bold;
              line-height: 1;
              color: #fff;
              content: 'A';
              background-color: #8fbdc3;
              border-radius: 7px;
            }

            &__text {
              @include lpo-text(1.6rem, $lpo-font-weight-regular);

              color: $lpo-color-text-black;
            }
          }
        }
      }
    }
  }
}
