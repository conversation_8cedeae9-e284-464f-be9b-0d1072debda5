/* stylelint-disable scss/dollar-variable-colon-space-after */
// フォント設定
// Google Fontsのインポート
@import 'https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@100;300;400;500;700;900&display=swap';
@import 'https://fonts.googleapis.com/css2?family=Yu+Gothic:wght@300;400;500;700&display=swap';

// Noto Sans JP
$lpo-font-family-noto: 'Noto Sans JP', helvetica, sans-serif;

// Yu Gothic
$lpo-font-family-yu:
  'Yu Gothic', 'YuGothic', 'Hiragino Kaku Gothic ProN', 'Hiragino Sans', meiryo, sans-serif;

// font-weight
// Figmaのfont-weight指定とCSS値の対応

// 共通用（どちらのフォントでも使用可能）
$lpo-font-weight-light: 300; // Light
$lpo-font-weight-regular: 400; // Regular/Normal
$lpo-font-weight-medium: 500; // Medium
$lpo-font-weight-semibold: 600; // Semi Bold
$lpo-font-weight-bold: 700; // Bold

// カラー
$lpo-color-text-red: #fa6b58;
$lpo-color-text-green: #3c8b86;
$lpo-color-text-light-green: #76aaaa;
$lpo-color-text-black: #333;
$lpo-color-text-gray: #8b8b8b;
$lpo-color-sumikuro: #5f6061;
$lpo-color-bg-grean: #e1ede8;
$lpo-color-bg-light-grean: #e0f3ee;
$lpo-color-bg-light-mint: #b1e2d5;
$lpo-color-bg-grid-line: #c0c0c0;
$lpo-color-bg-light-gray: #f9f9f9;
$lpo-color-bg-gray: #d9d9d9;
$lpo-color-mint: #3ab795;
$lpo-color-mosgreen: #e1ede8;
$lpo-color-border-green: #a0c1bb;
