module.exports = {
  files: [
    './mount_wordpress/wp-content/themes/appmart/*.{html,css,js,php}',
    './mount_wordpress/wp-content/themes/appmart/**/*.{html,css,js,php}',
    './mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.css',
    './mount_wordpress/wp-content/themes/appmart/assets/css/lpo/lpo.css',
    './mount_wordpress/wp-content/themes/appmart/s-lpo-page.php',
  ],
  proxy: 'http://wsl-ubuntu:20085',
  injectChanges: false,
  ghostMode: {
    clicks: false,
    scroll: false,
    forms: false,
  },
  host: 'wsl-ubuntu',
  open: false,
  port: 3000,
  ui: {
    port: 3001,
  },
  middleware: [
    function (req, res, next) {
      res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');
      next();
    },
  ],
};
